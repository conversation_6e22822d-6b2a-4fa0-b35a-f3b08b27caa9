// Utility functions for the application

/**
 * Format currency amount
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency symbol (default: $)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = '$') => {
  if (isNaN(amount)) return `${currency}0.00`;
  return `${currency}${parseFloat(amount).toFixed(2)}`;
};

/**
 * Format date to readable string
 * @param {string|Date} date - Date to format
 * @param {object} options - Intl.DateTimeFormat options
 * @returns {string} Formatted date string
 */
export const formatDate = (date, options = {}) => {
  if (!date) return '';
  
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };
  
  return new Date(date).toLocaleDateString('en-US', { ...defaultOptions, ...options });
};

/**
 * Calculate attendance percentage
 * @param {Array} attendances - Array of attendance records
 * @returns {object} Attendance statistics
 */
export const calculateAttendanceStats = (attendances) => {
  if (!attendances || attendances.length === 0) {
    return { attended: 0, missed: 0, total: 0, rate: 0 };
  }
  
  const attended = attendances.filter(a => a.attended).length;
  const missed = attendances.filter(a => !a.attended).length;
  const total = attendances.length;
  const rate = Math.round((attended / total) * 100);
  
  return { attended, missed, total, rate };
};

/**
 * Get initials from name
 * @param {string} name - Full name
 * @returns {string} Initials
 */
export const getInitials = (name) => {
  if (!name) return '';
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} length - Maximum length
 * @returns {string} Truncated text
 */
export const truncateText = (text, length = 100) => {
  if (!text || text.length <= length) return text;
  return text.substring(0, length) + '...';
};

/**
 * Check if user is authenticated
 * @returns {boolean} Authentication status
 */
export const isAuthenticated = () => {
  const token = localStorage.getItem('auth_token');
  const user = localStorage.getItem('user_data');
  return !!(token && user);
};

/**
 * Get user data from localStorage
 * @returns {object|null} User data or null
 */
export const getUserData = () => {
  try {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

/**
 * Clear authentication data
 */
export const clearAuthData = () => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user_data');
};

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Generate random ID
 * @returns {string} Random ID
 */
export const generateId = () => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} Is valid email
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Get payment status color class
 * @param {string} status - Payment status
 * @returns {string} CSS class name
 */
export const getPaymentStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'paid':
      return 'card__payment-status--paid';
    case 'pending':
      return 'card__payment-status--pending';
    case 'overdue':
      return 'card__payment-status--overdue';
    default:
      return 'card__payment-status--pending';
  }
};
