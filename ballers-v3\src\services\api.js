import axios from 'axios';
import { API_CONFIG } from '../config/api';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: API_CONFIG.HEADERS,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: async (email, password) => {
    const response = await api.post('/login', { email, password });
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/logout');
    return response.data;
  }
};

// Student API calls
export const studentAPI = {
  getProfile: async () => {
    const response = await api.get('/profile');
    return response.data;
  },

  getClasses: async () => {
    const response = await api.get('/classes');
    return response.data;
  },

  getAttendances: async () => {
    const response = await api.get('/attendances');
    return response.data;
  },

  getPayments: async () => {
    const response = await api.get('/payments');
    return response.data;
  }
};

export default api;
