// src/components/Auth/LoginPage.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import toast, { Toaster } from 'react-hot-toast';
import { useTranslation } from 'react-i18next'; // Import translation hook
import { useAuth } from '../../context/AuthContext';
import './LoginPage.css';

import Navbar from '../NavBar/Navbar'; // Import Navbar component
import Footer from '../Footer/Footer'; // Import Footer component
import AI from '../../components/AIAgent/AI'; // Import AI component


const slides = [
  { image: '/images/ball1.jpg', caption: 'Empowering Girl Athletes in Aqaba' },
  { image: '/images/ball5.jpg', caption: 'Rising Stars of Aqaba Basketball' },
  { image: '/images/ball2.jpg', caption: 'Strong, Fast, Focused – Future Champions' },
  { image: '/images/ball6.jpg', caption: 'From Aqaba Courts to the World Stage' },
  { image: '/images/ball3.jpg', caption: 'Training the Next Generation of Leaders' },
  { image: '/images/ball7.jpg', caption: 'Strength, Skill, Spirit' },
  { image: '/images/ball4.jpg', caption: 'Dribble. Shoot. Win.' },
  { image: '/images/ball8.jpg', caption: 'Chasing Greatness Every Day' },
];

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { t, i18n } = useTranslation(); // Get translation function and i18n instance
  const { login, isAuthenticated } = useAuth();

  const [currentSlide, setCurrentSlide] = useState(0);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated()) {
      navigate('/profile');
    }
  }, [isAuthenticated, navigate]);

  // Auto slide every 5 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await login(email, password);

      if (result.success) {
        // Show success toast
        toast.success(t('loginSuccessful'), {
          iconTheme: {
            primary: '#ef6c00',
            secondary: '#fff',
          },
        });

        // Redirect after delay
        setTimeout(() => {
          navigate('/profile');
        }, 1500);
      } else {
        setError(result.message || t('invalidCredentials'));
        toast.error(result.message || t('invalidCredentials'));
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(t('invalidCredentials'));
      toast.error(t('invalidCredentials'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Navbar /> {/* Render Navbar component */}
    <div className="auth-wrapper">
      {/* Toaster for notifications */}
      <Toaster />

      {/* Login Container */}
      <div className="auth-container">
        {/* Left Side: Login Form */}
        <div className="auth-form-section">
          <div className="auth-logo">{t('logo')}</div>
          <h2>{t('welcomeBack')}</h2>
          <p>{t('loginToAccessDashboard')}</p>
          <form onSubmit={handleLogin}>
            <input
              type="email"
              placeholder={t('email')}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <input
              type="password"
              placeholder={t('password')}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            {error && <p className="auth-error">{error}</p>}
            <button type="submit" disabled={loading}>
              {loading ? 'Logging in...' : t('login')}
            </button>
            <div className="auth-forgot-password">
              <a href="/forgot-password">{t('forgotPassword')}</a>
            </div>
          </form>
        </div>

        {/* Right Side: Image Carousel */}
        <div className="auth-image-section">
          <div className="auth-overlay"></div>

          {/* Slides */}
          {slides.map((slide, index) => (
            <div
              key={index}
              className={`auth-carousel-slide ${index === currentSlide ? 'active' : ''}`}
              style={{ backgroundImage: `url(${slide.image})` }}
            >
              <div className="auth-caption">
                <span>{slide.caption}</span>
              </div>
            </div>
          ))}

          {/* Dots Navigation */}
          <div className="auth-dots">
            {slides.map((_, i) => (
              <span
                key={i}
                className={`auth-dot ${i === currentSlide ? 'active' : ''}`}
                onClick={() => setCurrentSlide(i)}
              ></span>
            ))}
          </div>
        </div>
      </div>
    </div>

        <Footer /> {/* Render Footer component */}
        <AI /> {/* Render AI component */}
    </div>
  );
};

export default LoginPage;
