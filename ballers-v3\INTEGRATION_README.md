# Ballers Academy Frontend - Backend Integration

This document explains how the frontend has been updated to work with the Laravel backend API.

## Changes Made

### 1. Dependencies Added
- `axios` - HTTP client for API calls
- Updated package.json with axios dependency

### 2. New Files Created

#### API Layer
- `src/services/api.js` - API service layer with axios configuration
- `src/config/api.js` - API configuration and environment settings

#### Authentication
- `src/context/AuthContext.jsx` - React context for authentication state
- `src/components/ProtectedRoute.jsx` - Route protection component

#### Profile Component
- `src/components/PlayerProfile/PlayerProfile.jsx` - New profile component using real API data
- `src/components/PlayerProfile/PlayerProfile.css` - Styling for the profile component

#### Environment
- `.env.example` - Environment variables template
- `.env.local` - Local environment configuration

### 3. Updated Files

#### Main Application
- `src/main.tsx` - Added AuthProvider and protected routes
- `src/components/authh/login.jsx` - Updated to use real API authentication

## Setup Instructions

### 1. Install Dependencies
```bash
cd ballers-v3
npm install
```

### 2. Environment Configuration
Copy the environment file and update the API URL if needed:
```bash
cp .env.example .env.local
```

Edit `.env.local` to match your Laravel backend URL:
```
VITE_API_BASE_URL=http://localhost:8000/api
```

### 3. Backend Requirements
Make sure your Laravel backend is running on `http://localhost:8000` with the following endpoints:
- `POST /api/login` - Student authentication
- `GET /api/profile` - Student profile data
- `GET /api/classes` - Student's enrolled classes
- `GET /api/attendances` - Student's attendance records
- `GET /api/payments` - Student's payment history
- `POST /api/logout` - Logout endpoint

### 4. CORS Configuration
Ensure your Laravel backend has CORS configured to allow requests from your frontend domain.

## API Integration Details

### Authentication Flow
1. User enters email/password on login page
2. Frontend calls `POST /api/login` with credentials
3. Backend returns authentication token and student data
4. Token is stored in localStorage and used for subsequent requests
5. Protected routes check authentication status

### Data Mapping
The frontend has been updated to work with the actual backend data structure:

#### Student Profile
- `english_name` → displayed as main name
- `arabic_name` → displayed in parentheses
- `profile_photo` → used for avatar (with fallback)
- All backend fields are properly displayed

#### Classes
- Shows enrolled classes with instructor, schedule, and pricing
- Displays "No classes enrolled" if empty

#### Attendance
- Calculates attendance statistics from individual records
- Shows present/absent status with icons
- Displays attendance rate percentage

#### Payments
- Shows payment history with status indicators
- Calculates total due amount
- Displays account balance

## Features Implemented

### ✅ Working Features
- Real API authentication
- Protected routes
- Profile data display
- Classes listing
- Attendance tracking with statistics
- Payment history
- Logout functionality
- Loading states
- Error handling
- Responsive design

### 🔄 Removed Features (Not in Backend)
- Player numbers
- Badges system
- Team information
- Course information
- Performance statistics
- Media gallery
- Achievements

## Usage

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

## Testing

1. Start your Laravel backend server
2. Start the frontend development server
3. Navigate to `/login`
4. Use valid student credentials from your database
5. After login, you'll be redirected to the profile page with real data

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure Laravel CORS is configured properly
2. **API Connection**: Check if backend URL in `.env.local` is correct
3. **Authentication**: Verify student credentials exist in database
4. **Token Issues**: Clear localStorage if experiencing auth problems

### Debug Mode
The API service includes error logging. Check browser console for detailed error messages.

## Next Steps

1. Install dependencies: `npm install`
2. Configure environment variables
3. Start development server: `npm run dev`
4. Test with real backend data
5. Deploy to production when ready
