// API Configuration
export const API_CONFIG = {
  // Change this to your Laravel backend URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Default headers
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

// Environment-specific configurations
export const ENV_CONFIG = {
  development: {
    BASE_URL: 'http://localhost:8000/api',
    DEBUG: true,
  },
  production: {
    BASE_URL: 'https://your-production-domain.com/api',
    DEBUG: false,
  }
};

// Get current environment config
export const getCurrentConfig = () => {
  const env = import.meta.env.MODE || 'development';
  return ENV_CONFIG[env] || ENV_CONFIG.development;
};
